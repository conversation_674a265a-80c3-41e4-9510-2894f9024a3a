-- 检查数据库和表是否存在
-- 请在 MySQL 客户端中执行这些 SQL 语句

-- 1. 检查数据库是否存在
SHOW DATABASES LIKE 'xc402_system';

-- 2. 使用数据库
USE xc402_system;

-- 3. 检查 dictionary 表是否存在
SHOW TABLES LIKE 'dictionary';

-- 4. 查看 dictionary 表结构
DESCRIBE dictionary;

-- 5. 查看 dictionary 表中的数据
SELECT * FROM dictionary LIMIT 10;

-- 6. 检查表中数据总数
SELECT COUNT(*) as total_count FROM dictionary;

-- 如果表不存在，创建表的 SQL（根据实体类定义）
CREATE TABLE IF NOT EXISTS `dictionary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id标识',
  `name` varchar(255) DEFAULT NULL COMMENT '数据字典名称',
  `code` varchar(255) DEFAULT NULL COMMENT '数据字典代码',
  `item_values` text COMMENT '数据字典项--json格式',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典';

-- 插入一些测试数据
INSERT INTO `dictionary` (`name`, `code`, `item_values`) VALUES
('课程等级', '200001', '[{"sd_name": "初级", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "中级", "sd_id": "200002", "sd_status": "1"}, {"sd_name": "高级", "sd_id": "200003", "sd_status": "1"}]'),
('课程状态', '200002', '[{"sd_name": "未发布", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "已发布", "sd_id": "200002", "sd_status": "1"}]'),
('审核状态', '200003', '[{"sd_name": "未审核", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "审核通过", "sd_id": "200002", "sd_status": "1"}, {"sd_name": "审核不通过", "sd_id": "200003", "sd_status": "1"}]');
