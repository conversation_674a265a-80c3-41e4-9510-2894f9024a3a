import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接测试工具
 * 用于测试数据库连接是否正常
 */
public class TestDatabaseConnection {
    
    public static void main(String[] args) {
        // 测试不同的数据库连接配置
        testConnection("localhost", "xc402_system");
        testConnection("localhost", "xc402_content");
        testConnection("127.0.0.1", "xc402_system");
        testConnection("**************", "xc402_system");
    }
    
    private static void testConnection(String host, String database) {
        String url = String.format("************************************************************************", 
                                   host, database);
        String username = "root";
        String password = "mysql";
        
        System.out.println("测试连接: " + url);
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 连接成功: " + url);
            connection.close();
        } catch (ClassNotFoundException e) {
            System.out.println("❌ 驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            System.out.println("❌ 连接失败: " + url);
            System.out.println("   错误信息: " + e.getMessage());
        }
        System.out.println();
    }
}
