-- 数据库设置和检查脚本
-- 请在 MySQL 客户端中执行这些 SQL 语句

-- 1. 显示所有数据库
SHOW DATABASES;

-- 2. 创建系统管理数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS xc402_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 3. 创建内容管理数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS xc402_content DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 4. 使用系统管理数据库
USE xc402_system;

-- 5. 创建 dictionary 表
CREATE TABLE IF NOT EXISTS `dictionary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id标识',
  `name` varchar(255) DEFAULT NULL COMMENT '数据字典名称',
  `code` varchar(255) DEFAULT NULL COMMENT '数据字典代码',
  `item_values` text COMMENT '数据字典项--json格式',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典';

-- 6. 插入测试数据
INSERT INTO `dictionary` (`name`, `code`, `item_values`) VALUES
('课程等级', '200001', '[{"sd_name": "初级", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "中级", "sd_id": "200002", "sd_status": "1"}, {"sd_name": "高级", "sd_id": "200003", "sd_status": "1"}]'),
('课程状态', '200002', '[{"sd_name": "未发布", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "已发布", "sd_id": "200002", "sd_status": "1"}]'),
('审核状态', '200003', '[{"sd_name": "未审核", "sd_id": "200001", "sd_status": "1"}, {"sd_name": "审核通过", "sd_id": "200002", "sd_status": "1"}, {"sd_name": "审核不通过", "sd_id": "200003", "sd_status": "1"}]')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 7. 验证数据
SELECT * FROM dictionary;

-- 8. 使用内容管理数据库
USE xc402_content;

-- 9. 创建 course_base 表（示例）
CREATE TABLE IF NOT EXISTS `course_base` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `company_id` bigint(20) NOT NULL COMMENT '机构ID',
  `company_name` varchar(255) DEFAULT NULL COMMENT '机构名称',
  `name` varchar(100) NOT NULL COMMENT '课程名称',
  `users` varchar(500) DEFAULT NULL COMMENT '适用人群',
  `tags` varchar(32) DEFAULT NULL COMMENT '课程标签',
  `mt` varchar(20) NOT NULL COMMENT '大分类',
  `st` varchar(20) NOT NULL COMMENT '小分类',
  `grade` varchar(32) NOT NULL COMMENT '课程等级',
  `teachmode` varchar(32) NOT NULL COMMENT '教育模式',
  `description` text COMMENT '课程介绍',
  `pic` varchar(500) DEFAULT NULL COMMENT '课程图片',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `change_date` datetime DEFAULT NULL COMMENT '修改时间',
  `create_people` varchar(50) DEFAULT NULL COMMENT '创建人',
  `change_people` varchar(50) DEFAULT NULL COMMENT '更新人',
  `audit_status` varchar(10) NOT NULL COMMENT '审核状态',
  `status` varchar(10) NOT NULL DEFAULT '1' COMMENT '课程发布状态 未发布  已发布 下线',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程基本信息';

-- 10. 插入测试课程数据
INSERT INTO `course_base` (`company_id`, `company_name`, `name`, `users`, `tags`, `mt`, `st`, `grade`, `teachmode`, `description`, `audit_status`, `status`) VALUES
(1, '测试机构', 'Java基础课程', '初学者', 'Java,编程', '1-1', '1-1-1', '200001', '200002', 'Java基础课程描述', '202004', '203001'),
(1, '测试机构', 'Spring Boot实战', '有Java基础', 'SpringBoot,框架', '1-1', '1-1-2', '200002', '200002', 'Spring Boot实战课程', '202004', '203001')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 11. 验证课程数据
SELECT * FROM course_base LIMIT 5;
