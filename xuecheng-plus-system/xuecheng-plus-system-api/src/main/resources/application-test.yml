# 测试环境配置 - 用于验证数据库连接
server:
  servlet:
    context-path: /system
  port: 63110

spring:
  application:
    name: system-api
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************&
    username: root
    password: mysql
    # 连接池配置
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# 日志配置
logging:
  config: classpath:log4j2-dev.xml
  level:
    # 应用程序日志级别
    com.xuecheng: DEBUG
    # Spring 框架日志级别
    org.springframework: DEBUG
    # MyBatis 日志级别
    org.mybatis: DEBUG
    # 数据库连接池日志级别
    com.zaxxer.hikari: DEBUG
    # 根日志级别
    root: DEBUG

# Swagger 配置
swagger:
  title: "学成在线系统管理"
  description: "系统管理接口"
  base-package: com.xuecheng.system
  enabled: true
  version: 1.0.0

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    # 开启 MyBatis 的日志输出
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
