package com.xuecheng.system.controller;

import com.xuecheng.system.model.po.Dictionary;
import com.xuecheng.system.service.DictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

import java.util.List;

/**
 * <p>
 * 数据字典 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class DictionaryController  {

    @Autowired
    private DictionaryService dictionaryService;

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("message", "System API is running");
        result.put("timestamp", System.currentTimeMillis());
        log.info("健康检查接口被调用");
        return result;
    }

    @GetMapping("/dictionary/all")
    public List<Dictionary> queryAll() {
        try {
            log.info("开始查询所有字典数据");
            List<Dictionary> result = dictionaryService.queryAll();
            log.info("查询字典数据成功，共{}条记录", result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("查询字典数据失败", e);
            throw e;
        }
    }

    @GetMapping("/dictionary/code/{code}")
    public Dictionary getByCode(@PathVariable String code) {
        try {
            log.info("开始查询字典数据，code: {}", code);
            Dictionary result = dictionaryService.getByCode(code);
            log.info("查询字典数据成功，结果: {}", result != null ? "找到数据" : "未找到数据");
            return result;
        } catch (Exception e) {
            log.error("查询字典数据失败，code: {}", code, e);
            throw e;
        }
    }
}
